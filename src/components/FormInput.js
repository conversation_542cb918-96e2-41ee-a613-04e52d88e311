import React from 'react';
import { View, Text, TextInput } from 'react-native';

/**
 * FormInput Component
 * 
 * A reusable form input component that provides consistent styling and validation display
 * across all authentication screens. This component reduces code duplication and ensures
 * a uniform user experience.
 * 
 * Features:
 * - Consistent styling with Tailwind CSS classes
 * - Error state handling with visual feedback
 * - Accessibility support with proper labels
 * - Flexible configuration for different input types
 * - Loading state support
 * 
 * @param {Object} props - Component props
 * @param {string} props.label - The label text displayed above the input
 * @param {string} props.placeholder - Placeholder text for the input
 * @param {string} props.value - Current input value
 * @param {Function} props.onChangeText - Callback when text changes
 * @param {Function} props.onBlur - Callback when input loses focus
 * @param {string} props.error - Error message to display (if any)
 * @param {boolean} props.touched - Whether the field has been touched (for error display)
 * @param {boolean} props.editable - Whether the input is editable (default: true)
 * @param {boolean} props.secureTextEntry - Whether to hide text input (for passwords)
 * @param {string} props.keyboardType - Keyboard type (default, email-address, etc.)
 * @param {string} props.autoCapitalize - Auto-capitalization behavior
 * @param {string} props.autoComplete - Auto-complete hint for the input
 * @param {string} props.containerClassName - Additional CSS classes for the container
 * @param {string} props.inputClassName - Additional CSS classes for the input
 */
const FormInput = ({
  label,
  placeholder,
  value,
  onChangeText,
  onBlur,
  error,
  touched,
  editable = true,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'none',
  autoComplete,
  containerClassName = '',
  inputClassName = '',
}) => {
  // Determine if we should show the error (only if field is touched and has an error)
  const showError = touched && error;
  
  // Build the input CSS classes based on error state
  const inputClasses = `border rounded-xl p-4 text-base bg-white text-gray-800 ${
    showError ? 'border-red-500' : 'border-gray-200'
  } ${inputClassName}`;

  return (
    <View className={`mb-5 ${containerClassName}`}>
      {/* Input Label */}
      <Text className="text-base font-semibold text-gray-800 mb-2">
        {label}
      </Text>
      
      {/* Text Input */}
      <TextInput
        className={inputClasses}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        onBlur={onBlur}
        editable={editable}
        secureTextEntry={secureTextEntry}
        keyboardType={keyboardType}
        autoCapitalize={autoCapitalize}
        autoComplete={autoComplete}
        // Accessibility props for better user experience
        accessibilityLabel={label}
        accessibilityHint={placeholder}
        // Ensure proper text input behavior
        textContentType={secureTextEntry ? 'password' : autoComplete}
      />
      
      {/* Error Message */}
      {showError && (
        <Text className="text-red-500 text-sm mt-1 ml-1">
          {error}
        </Text>
      )}
    </View>
  );
};

export default FormInput;
