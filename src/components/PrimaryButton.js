import React from 'react';
import { TouchableOpacity, Text } from 'react-native';

/**
 * PrimaryButton Component
 * 
 * A reusable primary button component that provides consistent styling and behavior
 * across the application. This component ensures a uniform look and feel for all
 * primary actions like login, sign up, submit, etc.
 * 
 * Features:
 * - Consistent styling with Tailwind CSS classes
 * - Loading state with visual feedback
 * - Disabled state handling
 * - Accessibility support
 * - Flexible text and styling options
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - The button text to display
 * @param {string} props.loadingTitle - Text to show when loading (optional)
 * @param {Function} props.onPress - Callback when button is pressed
 * @param {boolean} props.loading - Whether the button is in loading state
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {string} props.className - Additional CSS classes for the button
 * @param {string} props.textClassName - Additional CSS classes for the text
 * @param {string} props.variant - Button variant ('primary', 'secondary', 'danger')
 */
const PrimaryButton = ({
  title,
  loadingTitle,
  onPress,
  loading = false,
  disabled = false,
  className = '',
  textClassName = '',
  variant = 'primary',
}) => {
  // Determine if button should be disabled (loading or explicitly disabled)
  const isDisabled = loading || disabled;
  
  // Get variant-specific styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'secondary':
        return {
          button: 'bg-gray-200 border border-gray-300',
          text: 'text-gray-800',
          disabledButton: 'bg-gray-100',
          disabledText: 'text-gray-400',
        };
      case 'danger':
        return {
          button: 'bg-red-500 shadow-lg',
          text: 'text-white',
          disabledButton: 'bg-red-300',
          disabledText: 'text-red-100',
        };
      case 'primary':
      default:
        return {
          button: 'bg-black shadow-lg',
          text: 'text-white',
          disabledButton: 'bg-gray-400',
          disabledText: 'text-gray-200',
        };
    }
  };

  const styles = getVariantStyles();
  
  // Build button CSS classes
  const buttonClasses = `rounded-xl p-4 items-center mt-5 ${
    isDisabled ? styles.disabledButton : styles.button
  } ${className}`;
  
  // Build text CSS classes
  const textClasses = `text-lg font-semibold ${
    isDisabled ? styles.disabledText : styles.text
  } ${textClassName}`;
  
  // Determine button text
  const buttonText = loading && loadingTitle ? loadingTitle : title;

  return (
    <TouchableOpacity
      className={buttonClasses}
      onPress={onPress}
      disabled={isDisabled}
      // Accessibility props
      accessibilityLabel={title}
      accessibilityHint={loading ? 'Button is loading' : 'Tap to perform action'}
      accessibilityState={{
        disabled: isDisabled,
        busy: loading,
      }}
    >
      <Text className={textClasses}>
        {buttonText}
      </Text>
    </TouchableOpacity>
  );
};

export default PrimaryButton;
