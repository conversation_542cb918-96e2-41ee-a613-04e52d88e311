# Components Directory

This directory contains reusable UI components that provide consistent styling and behavior across the OnRoad application.

## Available Components

### FormInput
**File:** `FormInput.js`

A reusable form input component that provides consistent styling and validation display across all authentication screens.

**Features:**
- Consistent styling with Tailwind CSS classes
- Error state handling with visual feedback
- Accessibility support with proper labels
- Flexible configuration for different input types
- Loading state support

**Usage:**
```jsx
import FormInput from '../components/FormInput';

<FormInput
  label="Email"
  placeholder="Enter your email"
  value={values.email}
  onChangeText={handleChange('email')}
  onBlur={handleBlur('email')}
  error={errors.email}
  touched={touched.email}
  keyboardType="email-address"
  autoComplete="email"
/>
```

### PrimaryButton
**File:** `PrimaryButton.js`

A reusable primary button component that provides consistent styling and behavior for all primary actions.

**Features:**
- Consistent styling with Tailwind CSS classes
- Loading state with visual feedback
- Disabled state handling
- Accessibility support
- Multiple variants (primary, secondary, danger)

**Usage:**
```jsx
import PrimaryButton from '../components/PrimaryButton';

<PrimaryButton
  title="Sign In"
  loadingTitle="Signing In..."
  onPress={handleSubmit}
  loading={isLoading}
  variant="primary"
/>
```

## Design Principles

### Consistency
All components follow the same design patterns and use consistent Tailwind CSS classes to ensure a uniform look and feel across the application.

### Accessibility
Components include proper accessibility labels, hints, and states to ensure the app is usable by all users.

### Flexibility
Components are designed to be flexible and configurable while maintaining consistency. They accept props for customization without breaking the design system.

### Documentation
Each component includes comprehensive JSDoc comments explaining its purpose, props, and usage examples.

## Adding New Components

When adding new reusable components:

1. **Follow the naming convention**: Use PascalCase for component names
2. **Include comprehensive documentation**: Add JSDoc comments explaining the component's purpose and all props
3. **Ensure accessibility**: Include proper accessibility props and labels
4. **Use consistent styling**: Follow the established Tailwind CSS patterns
5. **Add usage examples**: Include examples in the component documentation
6. **Update this README**: Add the new component to the list above

## Benefits of This Approach

### Reduced Code Duplication
By using reusable components, we've eliminated duplicate form input and button code across all authentication screens.

### Easier Maintenance
Changes to styling or behavior can be made in one place and automatically apply to all screens using the component.

### Consistent User Experience
All forms and buttons look and behave the same way, providing a professional and polished user experience.

### Better Accessibility
Accessibility features are built into the components and automatically available wherever they're used.
