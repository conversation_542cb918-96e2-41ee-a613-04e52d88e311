import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import authService from '../services/auth';
import FormInput from '../components/FormInput';
import PrimaryButton from '../components/PrimaryButton';

// Form validation schema using Yup
const ForgotPasswordSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
});

/**
 * ForgotPasswordScreen Component
 *
 * Handles password reset functionality for users who have forgotten their password.
 * Features:
 * - Email validation for password reset
 * - Supabase password reset email integration
 * - Success state management
 * - Navigation back to login screen
 */
const ForgotPasswordScreen = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  /**
   * Handles the password reset form submission
   * Sends a password reset email via Supabase
   */
  const handleResetPassword = async (values) => {
    setIsLoading(true);
    
    try {
      console.log('Password reset attempt:', values.email);
      
      // Attempt to reset password with Supabase
      const result = await authService.resetPassword(values.email);
      
      if (result.success) {
        setEmailSent(true);
        Alert.alert(
          'Success', 
          result.message || 'Password reset email sent! Please check your inbox.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate back to login screen
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Login' }],
                });
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Password reset failed. Please try again.');
      }
    } catch (error) {
      console.error('Password reset error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView 
          className="flex-1" 
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View className="flex-1 px-6 pt-15 pb-10">
            {/* App Logo/Title */}
            <View className="items-center mb-15">
              <Text className="text-4xl font-bold text-gray-800 mb-2">OnRoad</Text>
              <Text className="text-base text-gray-500 text-center">Real Estate Transactions Made Simple</Text>
            </View>

            {/* Reset Password Form */}
            <View className="justify-center">
              <Text className="text-3xl font-bold text-gray-800 mb-2 text-center">Reset Password</Text>
              <Text className="text-base text-gray-500 text-center mb-10">
                Enter your email address and we'll send you a link to reset your password.
              </Text>

              {!emailSent ? (
                <Formik
                  initialValues={{ email: '' }}
                  validationSchema={ForgotPasswordSchema}
                  onSubmit={handleResetPassword}
                >
                  {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                    <>
                      {/* Email Input Field */}
                      <FormInput
                        label="Email"
                        placeholder="Enter your email"
                        value={values.email}
                        onChangeText={handleChange('email')}
                        onBlur={handleBlur('email')}
                        error={errors.email}
                        touched={touched.email}
                        editable={!isLoading}
                        keyboardType="email-address"
                        autoComplete="email"
                      />

                      {/* Send Reset Link Button */}
                      <PrimaryButton
                        title="Send Reset Link"
                        loadingTitle="Sending..."
                        onPress={handleSubmit}
                        loading={isLoading}
                      />
                    </>
                  )}
                </Formik>
              ) : (
                <View className="items-center">
                  <View className="bg-green-100 border border-green-400 rounded-xl p-4 mb-5">
                    <Text className="text-green-700 text-center">
                      Password reset email sent! Please check your inbox and follow the instructions to reset your password.
                    </Text>
                  </View>
                  
                  <PrimaryButton
                    title="Back to Login"
                    onPress={() => navigation.reset({
                      index: 0,
                      routes: [{ name: 'Login' }],
                    })}
                  />
                </View>
              )}
            </View>

            {/* Footer Links */}
            <View className="items-center pt-10">
              <TouchableOpacity onPress={() => navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              })}>
                <Text className="text-blue-500 text-base font-medium mb-5">Back to Login</Text>
              </TouchableOpacity>

              <View className="flex-row items-center">
                <Text className="text-base text-gray-500">Don't have an account? </Text>
                <TouchableOpacity onPress={() => navigation.reset({
                  index: 0,
                  routes: [{ name: 'SignUp' }],
                })}>
                  <Text className="text-base text-blue-500 font-semibold">Sign Up</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ForgotPasswordScreen;
