import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import authService from '../services/auth';
import apiService from '../api';
import FormInput from '../components/FormInput';
import PrimaryButton from '../components/PrimaryButton';

const LoginSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
});

/**
 * LoginScreen Component
 *
 * Handles user authentication with email and password.
 * Features:
 * - Form validation using Formik and Yup
 * - Supabase authentication integration
 * - Comprehensive error handling with user-friendly messages
 * - Navigation to dashboard on successful login
 * - Links to sign-up and password reset flows
 */
const LoginScreen = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Handles the login form submission
   * Validates credentials with Supabase and navigates to dashboard on success
   */
  const handleLogin = async (values) => {
    setIsLoading(true);

    try {
      console.log('Login attempt for:', values.email);
      await proceedWithLogin(values);
    } catch (error) {
      console.error('Login process error:', error);
      setIsLoading(false);
    }
  };

  /**
   * Performs the actual login process with Supabase
   * Handles first-time sign-in API call and navigation to dashboard
   */
  const proceedWithLogin = async (values) => {
    try {
      // Authenticate user with Supabase
      const result = await authService.signIn(values.email, values.password);

      if (result.success) {
        console.log('Authentication successful for:', result.user.email);

        // Call first sign-in API for new users (optional - continues if it fails)
        try {
          console.log('[LOGIN] Calling firstSignIn API...');
          await apiService.firstSignIn();
          console.log('[LOGIN] First sign-in API call successful');
        } catch (firstSignInError) {
          // This is expected for returning users - continue with login
          console.warn('[LOGIN] First sign-in API call failed (expected for returning users):', firstSignInError.message);
        }

        // Navigate to dashboard on successful authentication
        navigation.reset({
          index: 0,
          routes: [{ name: 'Dashboard' }],
        });
        console.log('[LOGIN] Navigating to Dashboard');
      } else {
        // Handle authentication failure with user-friendly messages
        console.error('Login failed:', result.error);

        const errorMessage = result.error || result.originalError || 'Login failed. Please try again.';

        // Determine appropriate user message based on error type
        let alertTitle = 'Login Failed';
        let alertMessage = errorMessage;
        let alertButtons = [{ text: 'OK' }];

        // Handle specific error types with helpful messages
        if (errorMessage.includes('timeout') || errorMessage.includes('AuthRetryableFetchError')) {
          alertTitle = 'Connection Timeout';
          alertMessage = 'The login request timed out. Please check your internet connection and try again.';
          alertButtons = [
            { text: 'Retry', onPress: () => handleLogin(values) },
            { text: 'Cancel', style: 'cancel' }
          ];
        } else if (errorMessage.includes('Network request failed')) {
          alertTitle = 'Network Error';
          alertMessage = 'Unable to connect to the server. Please check your internet connection and try again.';
          alertButtons = [
            { text: 'Retry', onPress: () => handleLogin(values) },
            { text: 'Cancel', style: 'cancel' }
          ];
        } else if (errorMessage.includes('Invalid login credentials')) {
          alertTitle = 'Invalid Credentials';
          alertMessage = 'The email or password you entered is incorrect. Please check your credentials and try again.';
        } else if (errorMessage.includes('Email not confirmed')) {
          alertTitle = 'Email Not Verified';
          alertMessage = 'Please check your email and click the verification link before signing in.';
        }

        Alert.alert(alertTitle, alertMessage, alertButtons);
      }
    } catch (error) {
      console.error('Unexpected login error:', error);

      // Handle unexpected errors with simplified messaging
      let errorMessage = 'An unexpected error occurred during login.';

      if (error.message?.includes('timeout')) {
        errorMessage = 'Login request timed out. Please check your internet connection and try again.';
      } else if (error.message?.includes('Network')) {
        errorMessage = 'Network connection error. Please check your internet connection and try again.';
      }

      Alert.alert('Login Error', errorMessage, [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View className="flex-1 px-6 pt-15 pb-10">
            {/* App Logo/Title */}
            <View className="items-center mb-15">
              <Text className="text-4xl font-bold text-gray-800 mb-2">OnRoad</Text>
              <Text className="text-base text-gray-500 text-center">Real Estate Transactions Made Simple</Text>
            </View>

            {/* Login Form */}
            <View className="justify-center">
              <Text className="text-3xl font-bold text-gray-800 mb-2 text-center">Welcome Back</Text>
              <Text className="text-base text-gray-500 text-center mb-10">Sign in to your account</Text>

              <Formik
                initialValues={{ email: '', password: '' }}
                validationSchema={LoginSchema}
                onSubmit={handleLogin}
              >
                {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                  <>
                    {/* Email Input Field */}
                    <FormInput
                      label="Email"
                      placeholder="Enter your email"
                      value={values.email}
                      onChangeText={handleChange('email')}
                      onBlur={handleBlur('email')}
                      error={errors.email}
                      touched={touched.email}
                      editable={!isLoading}
                      keyboardType="email-address"
                      autoComplete="email"
                    />

                    {/* Password Input Field */}
                    <FormInput
                      label="Password"
                      placeholder="Enter your password"
                      value={values.password}
                      onChangeText={handleChange('password')}
                      onBlur={handleBlur('password')}
                      error={errors.password}
                      touched={touched.password}
                      editable={!isLoading}
                      secureTextEntry
                      autoComplete="password"
                    />

                    {/* Sign In Button */}
                    <PrimaryButton
                      title="Sign In"
                      loadingTitle="Signing In..."
                      onPress={handleSubmit}
                      loading={isLoading}
                    />
                </>
              )}
            </Formik>
          </View>

            {/* Footer Links - Navigation to other authentication screens */}
            <View className="items-center pt-5">
              <TouchableOpacity onPress={() => navigation?.reset({
                index: 0,
                routes: [{ name: 'ForgotPassword' }],
              })}>
                <Text className="text-blue-500 text-base font-medium mb-5">Forgot Password?</Text>
              </TouchableOpacity>

              <View className="flex-row items-center">
                <Text className="text-base text-gray-500">Don't have an account? </Text>
                <TouchableOpacity onPress={() => navigation?.reset({
                  index: 0,
                  routes: [{ name: 'SignUp' }],
                })}>
                  <Text className="text-base text-blue-500 font-semibold">Sign Up</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
