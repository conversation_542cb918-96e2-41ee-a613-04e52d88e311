import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ScrollView
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import authService from '../services/auth';
import FormInput from '../components/FormInput';
import PrimaryButton from '../components/PrimaryButton';

// Form validation schema using Yup
const SignUpSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().min(8, 'Password must be at least 8 characters').required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Please confirm your password'),
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
});

/**
 * SignUpScreen Component
 *
 * Handles new user registration with email and password.
 * Features:
 * - Form validation for all required fields
 * - Password confirmation validation
 * - Supabase user registration with metadata
 * - Email verification flow
 * - Navigation back to login on success
 */
const SignUpScreen = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Handles the sign-up form submission
   * Creates a new user account with Supabase and stores user metadata
   */
  const handleSignUp = async (values) => {
    setIsLoading(true);
    
    try {
      console.log('Sign up attempt:', values.email);
      
      // Attempt to sign up with Supabase
      const result = await authService.signUp(values.email, values.password, {
        first_name: values.firstName,
        last_name: values.lastName,
      });
      
      if (result.success) {
        Alert.alert(
          'Success', 
          result.message || 'Account created successfully! Please check your email for verification.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate back to login screen
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Login' }],
                });
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Sign up failed. Please try again.');
      }
    } catch (error) {
      console.error('Sign up error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView 
          className="flex-1" 
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View className="flex-1 px-6 pt-15 pb-10">
            {/* App Logo/Title */}
            <View className="items-center mb-10">
              <Text className="text-4xl font-bold text-gray-800 mb-2">OnRoad</Text>
              <Text className="text-base text-gray-500 text-center">Real Estate Transactions Made Simple</Text>
            </View>

            {/* Sign Up Form */}
            <View className="justify-center">
              <Text className="text-3xl font-bold text-gray-800 mb-2 text-center">Create Account</Text>
              <Text className="text-base text-gray-500 text-center mb-8">Join OnRoad today</Text>

              <Formik
                initialValues={{ 
                  email: '', 
                  password: '', 
                  confirmPassword: '',
                  firstName: '',
                  lastName: ''
                }}
                validationSchema={SignUpSchema}
                onSubmit={handleSignUp}
              >
                {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                  <>
                    {/* Name Fields Row */}
                    <View className="flex-row mb-5">
                      <View className="flex-1 mr-2">
                        <FormInput
                          label="First Name"
                          placeholder="First name"
                          value={values.firstName}
                          onChangeText={handleChange('firstName')}
                          onBlur={handleBlur('firstName')}
                          error={errors.firstName}
                          touched={touched.firstName}
                          editable={!isLoading}
                          autoCapitalize="words"
                          containerClassName="mb-0"
                        />
                      </View>

                      <View className="flex-1 ml-2">
                        <FormInput
                          label="Last Name"
                          placeholder="Last name"
                          value={values.lastName}
                          onChangeText={handleChange('lastName')}
                          onBlur={handleBlur('lastName')}
                          error={errors.lastName}
                          touched={touched.lastName}
                          editable={!isLoading}
                          autoCapitalize="words"
                          containerClassName="mb-0"
                        />
                      </View>
                    </View>

                    {/* Email Input Field */}
                    <FormInput
                      label="Email"
                      placeholder="Enter your email"
                      value={values.email}
                      onChangeText={handleChange('email')}
                      onBlur={handleBlur('email')}
                      error={errors.email}
                      touched={touched.email}
                      editable={!isLoading}
                      keyboardType="email-address"
                      autoComplete="email"
                    />

                    {/* Password Input Field */}
                    <FormInput
                      label="Password"
                      placeholder="Create a password"
                      value={values.password}
                      onChangeText={handleChange('password')}
                      onBlur={handleBlur('password')}
                      error={errors.password}
                      touched={touched.password}
                      editable={!isLoading}
                      secureTextEntry
                      autoComplete="new-password"
                    />

                    {/* Confirm Password Input Field */}
                    <FormInput
                      label="Confirm Password"
                      placeholder="Confirm your password"
                      value={values.confirmPassword}
                      onChangeText={handleChange('confirmPassword')}
                      onBlur={handleBlur('confirmPassword')}
                      error={errors.confirmPassword}
                      touched={touched.confirmPassword}
                      editable={!isLoading}
                      secureTextEntry
                      autoComplete="new-password"
                    />

                    {/* Create Account Button */}
                    <PrimaryButton
                      title="Create Account"
                      loadingTitle="Creating Account..."
                      onPress={handleSubmit}
                      loading={isLoading}
                    />
                </>
              )}
            </Formik>
          </View>

            {/* Footer Links */}
            <View className="items-center pt-5">
              <View className="flex-row items-center">
                <Text className="text-base text-gray-500">Already have an account? </Text>
                <TouchableOpacity onPress={() => navigation.reset({
                  index: 0,
                  routes: [{ name: 'Login' }],
                })}>
                  <Text className="text-base text-blue-500 font-semibold">Sign In</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default SignUpScreen;
